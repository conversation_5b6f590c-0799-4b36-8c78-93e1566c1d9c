<template>
  <div class="app-container">
    <!-- 动态背景 -->
    <div class="animated-bg">
      <div class="bg-animation"></div>
      <div class="particles">
        <div v-for="i in 100" :key="i" class="particle" :style="getParticleStyle(i)"></div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="main-content">
      <!-- 超炫酷标题 -->
      <div class="hero-section">
        <div class="title-container">
          <h1 class="main-title animate__animated animate__zoomIn animate__delay-1s">
            <span class="title-word" v-for="(word, index) in titleWords" :key="index" 
                  :style="{ animationDelay: (1.5 + index * 0.2) + 's' }">
              {{ word }}
            </span>
          </h1>
          <div class="title-glow"></div>
        </div>
        
        <p class="subtitle animate__animated animate__fadeInUp animate__delay-3s">
          🌟 极致视觉体验 · 未来科技感 · 炫酷到爆炸 🌟
        </p>
        
        <!-- 动态数据展示 -->
        <div class="stats-container animate__animated animate__slideInUp animate__delay-4s">
          <div v-for="(stat, index) in stats" :key="index" 
               class="stat-item animate__animated animate__pulse animate__infinite"
               :style="{ animationDelay: (4 + index * 0.5) + 's' }">
            <div class="stat-number">{{ stat.number }}</div>
            <div class="stat-label">{{ stat.label }}</div>
          </div>
        </div>
      </div>

      <!-- 3D卡片展示 -->
      <div class="cards-section animate__animated animate__fadeInUp animate__delay-5s">
        <div class="cards-grid">
          <div v-for="(card, index) in cards" :key="index"
               class="card-3d animate__animated"
               :class="card.animation"
               :style="{ animationDelay: (5.5 + index * 0.2) + 's' }"
               @mouseenter="handleCardHover(index)"
               @mouseleave="handleCardLeave(index)">
            <div class="card-inner">
              <div class="card-front">
                <div class="card-icon">{{ card.icon }}</div>
                <h3>{{ card.title }}</h3>
                <p>{{ card.description }}</p>
              </div>
              <div class="card-back">
                <div class="card-details">
                  <h4>{{ card.backTitle }}</h4>
                  <p>{{ card.backContent }}</p>
                  <div class="card-tech">{{ card.tech }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 交互控制台 -->
      <div class="control-panel animate__animated animate__bounceInUp animate__delay-7s">
        <div class="panel-header">
          <h3>🎮 炫酷控制台</h3>
        </div>
        <div class="panel-buttons">
          <el-button 
            type="primary" 
            size="large"
            class="cyber-btn"
            @click="triggerWaveEffect"
            :class="{ 'active-effect': activeEffect === 'wave' }">
            <el-icon><Lightning /></el-icon>
            波浪效果
          </el-button>
          
          <el-button 
            type="success" 
            size="large"
            class="cyber-btn"
            @click="triggerMatrixEffect"
            :class="{ 'active-effect': activeEffect === 'matrix' }">
            <el-icon><Grid /></el-icon>
            矩阵效果
          </el-button>
          
          <el-button 
            type="warning" 
            size="large"
            class="cyber-btn"
            @click="triggerGlitchEffect"
            :class="{ 'active-effect': activeEffect === 'glitch' }">
            <el-icon><Flash /></el-icon>
            故障效果
          </el-button>
        </div>
      </div>

      <!-- 特效层 -->
      <div v-if="showWaveEffect" class="wave-effect">
        <div v-for="i in 5" :key="i" 
             class="wave animate__animated animate__zoomIn"
             :style="{ animationDelay: i * 0.2 + 's' }"></div>
      </div>

      <div v-if="showMatrixEffect" class="matrix-effect">
        <div v-for="i in 20" :key="i" 
             class="matrix-line animate__animated animate__slideInDown"
             :style="getMatrixStyle(i)">
          {{ getMatrixText() }}
        </div>
      </div>

      <div v-if="showGlitchEffect" class="glitch-overlay animate__animated animate__flash">
        <div class="glitch-text">SYSTEM OVERLOAD</div>
      </div>
    </div>

    <!-- 浮动装饰元素 -->
    <div class="decorative-elements">
      <div v-for="i in 15" :key="i" 
           class="deco-element animate__animated animate__fadeInUp animate__infinite"
           :style="getDecoStyle(i)">
        {{ getDecoIcon(i) }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { Lightning, Grid, Flash } from '@element-plus/icons-vue'

// 响应式数据
const activeEffect = ref('')
const showWaveEffect = ref(false)
const showMatrixEffect = ref(false)
const showGlitchEffect = ref(false)

// 标题文字
const titleWords = ref(['CYBER', 'FUTURE', 'WORLD'])

// 统计数据
const stats = ref([
  { number: '99.9%', label: '性能提升' },
  { number: '1000+', label: '用户好评' },
  { number: '24/7', label: '全天候服务' },
  { number: '∞', label: '无限可能' }
])

// 3D卡片数据
const cards = ref([
  {
    icon: '🚀',
    title: '超光速引擎',
    description: '突破时空限制',
    backTitle: '量子加速技术',
    backContent: '采用最新量子计算技术，实现超光速数据处理',
    tech: 'Quantum Tech',
    animation: 'animate__rotateInDownLeft'
  },
  {
    icon: '🌐',
    title: '星际网络',
    description: '连接宇宙万物',
    backTitle: '全维度连接',
    backContent: '跨越维度的通信网络，实现真正的万物互联',
    tech: 'Cosmic Network',
    animation: 'animate__rotateInDownRight'
  },
  {
    icon: '🎯',
    title: '预知系统',
    description: '洞察未来趋势',
    backTitle: 'AI预测引擎',
    backContent: '基于深度学习的未来预测系统，准确率99.99%',
    tech: 'Future AI',
    animation: 'animate__rotateInUpLeft'
  },
  {
    icon: '⚡',
    title: '能量核心',
    description: '无限动力源泉',
    backTitle: '反物质引擎',
    backContent: '清洁无污染的反物质能源，提供无限动力',
    tech: 'Anti-Matter',
    animation: 'animate__rotateInUpRight'
  }
])

// 装饰图标
const decoIcons = ['⭐', '✨', '🌟', '💫', '🔥', '💎', '🌈', '⚡', '🚀', '🌌']
const matrixChars = ['0', '1', '█', '▓', '▒', '░', '◆', '◇', '◈', '◉']

// 方法
const getParticleStyle = (index) => {
  return {
    left: Math.random() * 100 + '%',
    top: Math.random() * 100 + '%',
    animationDelay: Math.random() * 10 + 's',
    animationDuration: (Math.random() * 5 + 3) + 's',
    opacity: Math.random() * 0.8 + 0.2
  }
}

const getDecoStyle = (index) => {
  return {
    left: Math.random() * 95 + '%',
    top: Math.random() * 90 + '%',
    animationDelay: Math.random() * 3 + 's',
    animationDuration: (Math.random() * 4 + 4) + 's',
    fontSize: (Math.random() * 15 + 15) + 'px',
    opacity: Math.random() * 0.6 + 0.3
  }
}

const getMatrixStyle = (index) => {
  return {
    left: (index * 5) + '%',
    animationDelay: Math.random() * 2 + 's',
    animationDuration: (Math.random() * 3 + 2) + 's'
  }
}

const getDecoIcon = (index) => {
  return decoIcons[index % decoIcons.length]
}

const getMatrixText = () => {
  return matrixChars[Math.floor(Math.random() * matrixChars.length)]
}

const handleCardHover = (index) => {
  // 3D卡片翻转效果
}

const handleCardLeave = (index) => {
  // 恢复卡片状态
}

const triggerWaveEffect = () => {
  activeEffect.value = 'wave'
  showWaveEffect.value = true
  
  setTimeout(() => {
    showWaveEffect.value = false
    activeEffect.value = ''
  }, 3000)
}

const triggerMatrixEffect = () => {
  activeEffect.value = 'matrix'
  showMatrixEffect.value = true
  
  setTimeout(() => {
    showMatrixEffect.value = false
    activeEffect.value = ''
  }, 4000)
}

const triggerGlitchEffect = () => {
  activeEffect.value = 'glitch'
  showGlitchEffect.value = true
  
  setTimeout(() => {
    showGlitchEffect.value = false
    activeEffect.value = ''
  }, 2000)
}

onMounted(() => {
  // 页面加载完成后的初始化
})
</script>

<style scoped>
/* 主容器 - 超炫酷背景 */
.app-container {
  min-height: 100vh;
  background:
    radial-gradient(circle at 20% 80%, #120458 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, #421a7a 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, #8b2635 0%, transparent 50%),
    linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
  position: relative;
  overflow: hidden;
  font-family: 'Orbitron', 'Arial', sans-serif;
}

/* 动态背景动画 */
.animated-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
}

.bg-animation {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    linear-gradient(45deg, transparent 30%, rgba(0, 255, 255, 0.03) 50%, transparent 70%),
    linear-gradient(-45deg, transparent 30%, rgba(255, 0, 255, 0.03) 50%, transparent 70%);
  animation: bgShift 8s ease-in-out infinite;
}

@keyframes bgShift {
  0%, 100% { transform: translateX(-100px) translateY(-100px) rotate(0deg); }
  50% { transform: translateX(100px) translateY(100px) rotate(180deg); }
}

/* 粒子系统 */
.particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.particle {
  position: absolute;
  width: 2px;
  height: 2px;
  background: linear-gradient(45deg, #00ffff, #ff00ff);
  border-radius: 50%;
  animation: particleFloat 8s linear infinite;
  box-shadow: 0 0 6px currentColor;
}

@keyframes particleFloat {
  0% {
    transform: translateY(100vh) translateX(0) rotate(0deg);
    opacity: 0;
  }
  10% { opacity: 1; }
  90% { opacity: 1; }
  100% {
    transform: translateY(-100px) translateX(100px) rotate(360deg);
    opacity: 0;
  }
}

/* 主要内容区域 */
.main-content {
  position: relative;
  z-index: 10;
  color: #ffffff;
  padding: 2rem;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

/* 超炫酷标题区域 */
.hero-section {
  text-align: center;
  margin-bottom: 4rem;
}

.title-container {
  position: relative;
  margin-bottom: 2rem;
}

.main-title {
  font-size: 5rem;
  font-weight: 900;
  letter-spacing: 0.1em;
  margin: 0;
  position: relative;
  display: flex;
  justify-content: center;
  gap: 1rem;
}

.title-word {
  display: inline-block;
  background: linear-gradient(45deg, #00ffff, #ff00ff, #ffff00, #00ff00);
  background-size: 400% 400%;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  animation:
    titleGlow 3s ease-in-out infinite,
    titleFloat 4s ease-in-out infinite;
  text-shadow: 0 0 30px rgba(0, 255, 255, 0.5);
  filter: drop-shadow(0 0 20px rgba(255, 0, 255, 0.3));
}

@keyframes titleGlow {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

@keyframes titleFloat {
  0%, 100% { transform: translateY(0px) rotateX(0deg); }
  50% { transform: translateY(-10px) rotateX(5deg); }
}

.title-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 120%;
  height: 120%;
  background: radial-gradient(ellipse, rgba(0, 255, 255, 0.2) 0%, transparent 70%);
  animation: glowPulse 2s ease-in-out infinite;
  z-index: -1;
}

@keyframes glowPulse {
  0%, 100% { opacity: 0.3; transform: translate(-50%, -50%) scale(1); }
  50% { opacity: 0.8; transform: translate(-50%, -50%) scale(1.1); }
}

.subtitle {
  font-size: 1.8rem;
  color: #00ffff;
  text-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
  margin-bottom: 3rem;
  animation: subtitleGlow 2s ease-in-out infinite alternate;
}

@keyframes subtitleGlow {
  from { text-shadow: 0 0 20px rgba(0, 255, 255, 0.5); }
  to { text-shadow: 0 0 30px rgba(0, 255, 255, 0.8), 0 0 40px rgba(255, 0, 255, 0.3); }
}

/* 统计数据展示 */
.stats-container {
  display: flex;
  gap: 3rem;
  justify-content: center;
  flex-wrap: wrap;
  margin-bottom: 3rem;
}

.stat-item {
  text-align: center;
  padding: 1.5rem;
  background: rgba(0, 255, 255, 0.1);
  border: 2px solid rgba(0, 255, 255, 0.3);
  border-radius: 15px;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-5px) scale(1.05);
  border-color: rgba(255, 0, 255, 0.6);
  box-shadow: 0 10px 30px rgba(0, 255, 255, 0.3);
}

.stat-number {
  font-size: 2.5rem;
  font-weight: bold;
  color: #00ffff;
  text-shadow: 0 0 15px rgba(0, 255, 255, 0.8);
}

.stat-label {
  font-size: 1rem;
  color: #ffffff;
  opacity: 0.8;
  margin-top: 0.5rem;
}
</style>
