<template>
  <div>
    <el-switch
      v-model="switchValue"
      :active-value="true"
      :inactive-value="false"
      @change="change"
    ></el-switch>

    <!--
    const switchValue = ref(false)
    const change = (e) => {
      console.log('change', e)
    }
    -->
    <el-button type="primary" size="default" @click="handleClick">确认</el-button>
  </div>
</template>

<script setup>
import { ref } from "vue"

const switchValue = ref("")
const handleClick = () => {
  console.log("666")
}
const change = () => {
  console.log("change")
}

const fuck = () => {
  console.log("fuck")
}
</script>

<style lang="scss" scoped></style>
